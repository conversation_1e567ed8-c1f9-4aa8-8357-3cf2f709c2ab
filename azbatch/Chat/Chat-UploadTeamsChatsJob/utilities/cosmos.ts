import { Con<PERSON><PERSON>, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData, ITeamsChatsMessages } from "./models";
import { CustomLogger } from "./log";
import { updateTableStorageChatsStatus } from "./tableStorage";

import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDB<PERSON>ey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertChatsMessages(
  logger: CustomLogger,
  dataChatsMessages: IBatchResponseData[]
): Promise<void> {

  // logger.log(`[CosmosDB:insertChatsMessages] dataChatsMessages: ${JSON.stringify(dataChatsMessages)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:insertChatsMessages] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataChatsMessages: ITeamsChatsMessages[][] = dataChatsMessages
      .map((chatsMessages) => chatsMessages.body?.value || [])
      .filter(Array.isArray);

    const insertedCount = await processChatsMessages(container, modifiedDataChatsMessages, logger);

    logger.log(`[CosmosDB:insertChatsMessages] Inserted: ${insertedCount} New CHATS_MESSAGES to Cosmos DB`);
    const totalMessageCount = modifiedDataChatsMessages.reduce((count, array) => count + (array ? array.length : 0),0 );
    logger.log(`[CosmosDB:insertChatsMessages] Skipped: ${totalMessageCount - insertedCount} Existing CHATS_MESSAGES`);
  } catch (error) {
    logger.log(`[CosmosDB:insertChatsMessages] Error Processing Messages: ${error}`);
    throw error;
  }
}

async function processChatsMessages(
  container: Container,
  modifiedDataChatsMessages: ITeamsChatsMessages[][],
  logger: CustomLogger
): Promise<number> {
  let insertedCount = 0;

  logger.log(`[CosmosDB:processChatsMessages] Total modifiedDataChatsMessages to Process: ${modifiedDataChatsMessages.length}`);
  // logger.log(`[CosmosDB:processChatsMessages] modifiedDataChatsMessages: ${JSON.stringify(modifiedDataChatsMessages)}`); // !!!

  for (const messageArray of modifiedDataChatsMessages) {
    for (const chatsMessages of messageArray) {
      if (!chatsMessages.id) {
        logger.log(`[CosmosDB:Process] Error: chatsMessages.id is undefined for chatsMessages: ${JSON.stringify(chatsMessages.id)}`);
        continue;
      }
      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: chatsMessages.id }]
      };
      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();
        if (existingMessages.length === 0) {
          await container.items.create(chatsMessages);
          insertedCount++;
        }
      } catch (error) {
        logger.log(`[CosmosDB:processChatsMessages] Error processing Message: ${error}`);
      }
    }
  }
  return insertedCount;
}

export async function updateChatsMembers(
  logger: CustomLogger,
  dataChatMembers: IBatchResponseData[]
): Promise<void> {

  logger.log(`[CosmosDB:updateChatsMembers] Total dataChatMembers to Process: ${dataChatMembers.length}`);
  // logger.log(`[CosmosDB:updateChatsMembers] dataChatMembers: ${JSON.stringify(dataChatMembers)}`); // !!!

  try {
    // Validate inputs
    if (!dataChatMembers || dataChatMembers.length === 0) {
      logger.log(`[CosmosDB:updateChatsMembers] No CHATS_MEMBERS data to process`);
      return;
    }
    
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:updateChatsMembers] Database name and container name must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);
    
    const modifiedDataChatsMembers = dataChatMembers
      .filter(chat => chat.id && chat.body?.value)
      .map(chat => ({
        chatId: chat.id,
        members: chat.body?.value || []
      }));
    
    const updatedMessagesCount = await processChatMembers(container, modifiedDataChatsMembers, logger);
    logger.log(`[CosmosDB:updateChatsMembers] Successfully Updated ${updatedMessagesCount} Messages in Cosmos DB`);
  } catch (error) {
    logger.log(`[CosmosDB:updateChatsMembers] Error processing CHATS_MEMBERS: ${error}`);
    throw error;
  }
}

async function processChatMembers(
  container: Container,
  modifiedDataChatsMembers: any[],
  logger: CustomLogger
): Promise<number> {
  let totalUpdatedCount = 0;
  
  logger.log(`[CosmosDB:processChatMembers] Total modifiedDataChatsMembers to Process: ${modifiedDataChatsMembers.length}`);
  // logger.log(`[CosmosDB:processChatMembers] modifiedDataChatsMembers: ${JSON.stringify(modifiedDataChatsMembers)}`);

  for (const chatEntry of modifiedDataChatsMembers) {
    const chatId = chatEntry.chatId;
    const members = chatEntry.members;

    if (!members || !Array.isArray(members) || members.length === 0) {
      logger.log(`[CosmosDB:processChatMembers] Skipping chat ${chatId} - No Valid Members`);
      continue;
    }
    
    try {
      // Query Chat Messages
      const allMessages = await queryChatsMessages(container, chatId, logger);
      // logger.log(`[CosmosDB:processChatMembers] allMessages: ${JSON.stringify(allMessages)}`); // !!!

      if (allMessages.length === 0) {
        logger.log(`[CosmosDB:processChatMembers] No Messages Found for chatId: ${chatId}`);
        continue;
      }
      
      // Compare members visibilityHistoryStartDateTime with message lastModifiedDateTime
      let chatUpdatedCount = 0;
      for (const chatMessages of allMessages) {
        const wasUpdated = await processChatsMessagesMembers(chatMessages, members, container, logger);
        if (wasUpdated) {
          chatUpdatedCount++;
          totalUpdatedCount++;
        }
      }
      logger.log(`[CosmosDB:processChatMembers] Completed ${chatUpdatedCount} of ${allMessages.length} Messages Updated`);
      allMessages.length = 0;

      // Update Table Storage - lastCheckedDatetime
      await updateTableStorageChatsStatus(logger, chatEntry);
      
    } catch (error) {
      logger.log(`[CosmosDB:processChatMembers] Error Processing chatId ${chatId}: ${error}`);
    }
  }
  return totalUpdatedCount;
}

async function queryChatsMessages(container: Container, chatId: string, logger: CustomLogger) {
  const querySpec = {
    query: "SELECT * FROM c WHERE c.chatId = @chatId",
    parameters: [{ name: "@chatId", value: chatId }]
  };
  
  const { resources: messages } = await container.items.query(querySpec).fetchAll();
  logger.log(`[CosmosDB:queryChatsMessages] Found ${messages.length} Messages for chatId: ${chatId}`);
  
  return messages;
}


async function processChatsMessagesMembers(
  chatMessages: any, 
  members: any[], 
  container: Container, 
  logger: CustomLogger
): Promise<boolean> {
  const messageLastModifiedDatetime = chatMessages.lastModifiedDateTime;

  if (!messageLastModifiedDatetime) {
    logger.log(`[CosmosDB:processChatsMessagesMembers] Skipping chatMessages: ${chatMessages.id} - Missing lastModifiedDateTime`);
    return false;
  }
  
  const messageDate = new Date(messageLastModifiedDatetime);
  if (isNaN(messageDate.getTime())) {
    logger.log(`[CosmosDB:processChatsMessagesMembers] Skipping chatMessages: ${chatMessages.id} - Invalid Date: ${messageLastModifiedDatetime}`);
    return false;
  }

  const eligibleUserIds = checkUserVisibilityHistory(members, messageDate);
  
  // Get existing security_user_id array (or empty array if not exists)
  const existingSecurityUserIds = chatMessages.security_user_id || [];
  
  // Merge existing and new eligible users, removing duplicates
  const mergedSecurityUserIds = [...new Set([...existingSecurityUserIds, ...eligibleUserIds])];
  
  // Compare current vs merged to see if update is needed
  const currentSecurity = JSON.stringify(existingSecurityUserIds.slice().sort());
  const newSecurity = JSON.stringify(mergedSecurityUserIds.slice().sort());
  
  if (currentSecurity === newSecurity) {
    // logger.log(`[CosmosDB:processChatsMessagesMembers] No changes needed for chatMessages ${chatMessages.id} - all eligible users already present`);
    return false;
  }

  try {
    chatMessages.security_user_id = mergedSecurityUserIds;
    await container.items.upsert(chatMessages);
    
    // logger.log(`[CosmosDB:processChatsMessagesMembers] Updated chatMessages ${chatMessages.id} - added ${mergedSecurityUserIds.length - existingSecurityUserIds.length} new eligible users (total: ${mergedSecurityUserIds.length})`);
    return true;
    
  } catch (error) {
    logger.log(`[CosmosDB:processChatsMessagesMembers] Failed to Update chatMessages ${chatMessages.id}: ${error}`);
    return false;
  }
}

function checkUserVisibilityHistory(members: any[], messageDate: Date): string[] {
  return members
    .filter(member => {
      if (!member.userId) return false;
      
      const memberJoinDate = new Date(member.visibleHistoryStartDateTime || "0001-01-01T00:00:00Z");
      if (isNaN(memberJoinDate.getTime())) return false;
      
      return messageDate >= memberJoinDate;
    })
    .map(member => member.userId);
}