import * as dotenv from 'dotenv';

dotenv.config();

const SPO_LIST_ITEMS_SEARCH_SIZE: string = process.env['SPO_LIST_ITEMS_SEARCH_SIZE'] ?? '50';
const SPO_LIST_ITEMS_START_DATE: string = process.env['SPO_LIST_ITEMS_START_DATE'] ?? '2025-01-01';
const SPO_LIST_ITEMS_END_DATE: string = process.env['SPO_LIST_ITEMS_END_DATE'] ?? '2025-12-31';

interface SharePointItemRequest {
  id: string;
  method: string;
  url: string;
  headers: {
    'Prefer': string;
  };
}

// *** Use this function to create date chunks for each month ***

// function createMonthlyChunks(startDate: Date, endDate: Date): Array<{ start: string; end: string }> {
//   const chunks = [];
  
//   // Start from the start date's year and month
//   let currentYear = startDate.getFullYear();
//   let currentMonth = startDate.getMonth();
  
//   while (true) {
//     // Get the first day of the current month
//     const monthStart = new Date(currentYear, currentMonth, 1);
    
//     // Get the last day of the current month (day 0 of next month)
//     const monthEnd = new Date(currentYear, currentMonth + 1, 0);
    
//     // If month start is beyond our end date, break
//     if (monthStart > endDate) {
//       break;
//     }
    
//     // Determine the actual start and end for this chunk
//     const chunkStart = monthStart < startDate ? startDate : monthStart;
//     const chunkEnd = monthEnd > endDate ? endDate : monthEnd;
    
//     // Format dates as YYYY-MM-DD, ensuring we use local date components
//     const startStr = chunkStart.getFullYear() + '-' + 
//                      String(chunkStart.getMonth() + 1).padStart(2, '0') + '-' + 
//                      String(chunkStart.getDate()).padStart(2, '0');
    
//     const endStr = chunkEnd.getFullYear() + '-' + 
//                    String(chunkEnd.getMonth() + 1).padStart(2, '0') + '-' + 
//                    String(chunkEnd.getDate()).padStart(2, '0');
    
//     chunks.push({
//       start: startStr,
//       end: endStr
//     });
    
//     // Move to the next month
//     currentMonth++;
//     if (currentMonth > 11) {
//       currentMonth = 0;
//       currentYear++;
//     }
//   }
  
//   return chunks;
// }

// export function createListItemRequests(siteId: string, listId: string): SharePointItemRequest[] {
//   if (!siteId || !listId) return [];

//   const startDate = new Date(SPO_LIST_ITEMS_START_DATE);
//   const endDate = new Date(SPO_LIST_ITEMS_END_DATE);
//   const requests: SharePointItemRequest[] = [];

//   const dateChunks = createMonthlyChunks(startDate, endDate);
  
//   // Create batch requests for each monthly chunk
//   dateChunks.forEach((chunk, index) => {
//     const dateFilter = `fields/Modified gt '${chunk.start}T00:00:00Z' and fields/Modified lt '${chunk.end}T23:59:59Z'`;
    
//     requests.push({
//       id: `${siteId}_${listId}_month${index + 1}`,
//       method: 'GET',
//       url: `/sites/${siteId}/lists/${listId}/items?$top=${SPO_LIST_ITEMS_SEARCH_SIZE}&$filter=${dateFilter}&$expand=fields&$select=sharepointIds`,
//       headers: {
//         'Prefer': 'HonorNonIndexedQueriesWarningMayFailRandomly'
//       }
//     });
//   });
  
//   return requests;
// }

export function createListItemRequests(id: string, siteId: string, listId: string): SharePointItemRequest[] {
  if (!siteId || !listId) return [];

  const dateFilter = `fields/Modified ge '${SPO_LIST_ITEMS_START_DATE}T00:00:00Z' and fields/Modified le '${SPO_LIST_ITEMS_END_DATE}T23:59:59Z'`;
    
  return [{
      id: `${id}`,
      method: 'GET',
      url: `/sites/${siteId}/lists/${listId}/items?$top=${SPO_LIST_ITEMS_SEARCH_SIZE}&$filter=${dateFilter}&$expand=fields&$select=webUrl,sharepointIds`,
      headers: {
        'Prefer': 'HonorNonIndexedQueriesWarningMayFailRandomly'
      }
  }];
}