import * as React from 'react';
import { EventReporter, EventReportType } from '@avanade-teams/app-insights-reporter';
import useTeamsChatsApiAccessor, { PostTeamsChatsApi, DeleteTeamsChatsApi } from '../accessors/useTeamsChatsApiAccessor';
import { IUserChatItem } from '../accessors/useUserChatsAndChannelsAccessor';
import { ITeamsChatsItem, IRepositoryTeamsChatsQueue } from '../../types/IGeraniumAttaneDB';
import { UseTeamsChatsRepositoryReturnType } from '../accessors/useTeamsChatsRepositoryAccessor';

// エラー定数
export const UseRemoteTeamsChatsError = {
  MISSING_PARAMS: 'MISSING_PARAMS',
  NO_TOKENS: 'NO_TOKENS',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
  IS_FIRST_TIME_SYNC: 'IS_FIRST_TIME_SYNC',
  QUEUE_SEND_ERROR: 'QUEUE_SEND_ERROR',
};

/**
 * キューを直列で逐次送信し、成功したキューは削除する
 * 途中で失敗した場合はキューを保持し、送信を止める
 */
export async function sendTeamsChatsQueues(
  queues: IRepositoryTeamsChatsQueue[] | null,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  deleteTeamsChatsQueue: ((id: string) => Promise<void>) | undefined,
  eventReporter: EventReporter,
): Promise<void> {
  if (!queues || !postTeamsChatsApi || !deleteTeamsChatsApi || !deleteTeamsChatsQueue) {
    return Promise.resolve();
  }

  // 0件時はwhileを回さずに終了
  if (queues.length === 0) {
    return Promise.resolve();
  }

  // キューを順次処理（エラー時は停止）
  const queuesCopy = [...queues];
  let processedCount = 0;
  const maxRetry = queues.length;

  try {
    await queuesCopy.reduce(async (previousPromise, queue) => {
      await previousPromise;

      if (processedCount >= maxRetry) {
        return;
      }

      processedCount += 1;

      if (queue.type === 'PUT') {
        const request = {
          countId: queue.data.countId,
          chatType: queue.data.chatType,
          ...(queue.data.type === 'チャット' && {
            chatId: queue.data.id,
          }),
          ...(queue.data.type === 'チャネル' && {
            teamId: queue.data.teamId,
            channelId: queue.data.id,
          }),
        };

        await postTeamsChatsApi(request);
      } else if (queue.type === 'DELETE') {
        await deleteTeamsChatsApi(queue.data.id);
      }

      // 成功したキューを削除
      await deleteTeamsChatsQueue(queue.data.id);

      // 元の配列からも削除
      queues.shift();
    }, Promise.resolve());
  } catch (e) {
    // エラーが発生した場合は処理を停止
    const currentQueue = queuesCopy[processedCount - 1];
    eventReporter({
      type: EventReportType.SYS_ERROR,
      name: UseRemoteTeamsChatsError.QUEUE_SEND_ERROR,
      error: e as Error,
      customProperties: {
        queueLength: queues.length,
        currentQueueType: currentQueue?.type,
        currentQueueId: currentQueue?.data.id,
      },
    });
  }

  return Promise.resolve();
}

/**
 * TeamsChatsアイテムをリモートに追加する
 */
export async function addRemoteTeamsChatsImpl(
  item: ITeamsChatsItem,
  addTeamsChatsQueue: ((data: ITeamsChatsItem, type: 'PUT' | 'DELETE') => Promise<void>) | undefined,
  getTeamsChatsQueues: (() => Promise<IRepositoryTeamsChatsQueue[]>) | undefined,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  deleteTeamsChatsQueue: ((id: string) => Promise<void>) | undefined,
  eventReporter: EventReporter,
): Promise<void> {
  if (!addTeamsChatsQueue
    || !getTeamsChatsQueues
    || !postTeamsChatsApi
    || !deleteTeamsChatsApi
    || !deleteTeamsChatsQueue) {
    return Promise.reject(new Error(UseRemoteTeamsChatsError.MISSING_PARAMS));
  }

  // キューとTeamsChatsの両方に追加
  await addTeamsChatsQueue(item, 'PUT');

  // キュー送信を試行
  return sendTeamsChatsQueues(
    await getTeamsChatsQueues(),
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
  );
}

/**
 * 複数のTeamsChatsアイテムをリモートに追加する
 */
export async function addMultipleRemoteTeamsChatsImpl(
  items: ITeamsChatsItem[],
  addTeamsChatsQueue: ((data: ITeamsChatsItem, type: 'PUT' | 'DELETE') => Promise<void>) | undefined,
  getTeamsChatsQueues: (() => Promise<IRepositoryTeamsChatsQueue[]>) | undefined,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  deleteTeamsChatsQueue: ((id: string) => Promise<void>) | undefined,
  eventReporter: EventReporter,
): Promise<void> {
  if (!addTeamsChatsQueue
    || !getTeamsChatsQueues
    || !postTeamsChatsApi
    || !deleteTeamsChatsApi
    || !deleteTeamsChatsQueue) {
    return Promise.reject(new Error(UseRemoteTeamsChatsError.MISSING_PARAMS));
  }

  // 全てのアイテムをキューに追加（順次処理）
  await items.reduce(async (previousPromise, item) => {
    await previousPromise;
    await addTeamsChatsQueue(item, 'PUT');
  }, Promise.resolve());

  // キュー送信を試行
  return sendTeamsChatsQueues(
    await getTeamsChatsQueues(),
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
  );
}

/**
 * TeamsChatsアイテムをリモートから削除する
 */
export async function deleteRemoteTeamsChatsImpl(
  item: ITeamsChatsItem,
  addTeamsChatsQueue: ((data: ITeamsChatsItem, type: 'PUT' | 'DELETE') => Promise<void>) | undefined,
  getTeamsChatsQueues: (() => Promise<IRepositoryTeamsChatsQueue[]>) | undefined,
  postTeamsChatsApi: PostTeamsChatsApi | undefined,
  deleteTeamsChatsApi: DeleteTeamsChatsApi | undefined,
  deleteTeamsChatsQueue: ((id: string) => Promise<void>) | undefined,
  eventReporter: EventReporter,
): Promise<void> {
  if (!addTeamsChatsQueue
    || !getTeamsChatsQueues
    || !postTeamsChatsApi
    || !deleteTeamsChatsApi
    || !deleteTeamsChatsQueue) {
    return Promise.reject(new Error(UseRemoteTeamsChatsError.MISSING_PARAMS));
  }

  // キューとTeamsChatsの両方から削除
  await addTeamsChatsQueue(item, 'DELETE');

  // キュー送信を試行
  return sendTeamsChatsQueues(
    await getTeamsChatsQueues(),
    postTeamsChatsApi,
    deleteTeamsChatsApi,
    deleteTeamsChatsQueue,
    eventReporter,
  );
}

/**
 * IUserChatItemからITeamsChatsItemに変換する
 */
export function convertUserChatItemToTeamsChatsItem(
  item: IUserChatItem,
  countId: number,
): ITeamsChatsItem {
  return {
    id: item.id,
    name: item.name,
    type: item.type,
    chatType: item.chatType,
    teamId: item.teamId,
    countId,
  };
}

/**
 * 複数のIUserChatItemからITeamsChatsItemの配列に変換する
 */
export function convertUserChatItemsToTeamsChatsItems(
  items: IUserChatItem[],
): ITeamsChatsItem[] {
  return items.map((item, index) => convertUserChatItemToTeamsChatsItem(item, index + 1));
}

export type UseRemoteTeamsChatsFeatureReturnType = {
  addRemoteTeamsChats: ((item: ITeamsChatsItem) => Promise<void>) | undefined;
  addMultipleRemoteTeamsChats: ((items: ITeamsChatsItem[]) => Promise<void>) | undefined;
  deleteRemoteTeamsChats: ((item: ITeamsChatsItem) => Promise<void>) | undefined;
  sendQueues: (() => Promise<void>) | undefined;
  convertUserChatItems: (items: IUserChatItem[]) => ITeamsChatsItem[];
};

/**
 * リモート上のAPIとブラウザローカルのTeamsChatsデータの追加/同期処理をする機能
 */
const useRemoteTeamsChatsFeature = (
  useRepositoryReturn: UseTeamsChatsRepositoryReturnType,
  useTeamsChatsApiReturn: ReturnType<typeof useTeamsChatsApiAccessor>,
  eventReporter: EventReporter,
): UseRemoteTeamsChatsFeatureReturnType => {
  // useRepositoryからメンバ取得
  const {
    addTeamsChatsQueue,
    getTeamsChatsQueues,
    deleteTeamsChatsQueue,
  } = useRepositoryReturn;

  // useTeamsChatsApiからメンバ取得
  const { postTeamsChatsApi, deleteTeamsChatsApi } = useTeamsChatsApiReturn;

  const addRemoteTeamsChats = React.useCallback(
    async (item: ITeamsChatsItem) => addRemoteTeamsChatsImpl(
      item,
      addTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      deleteTeamsChatsQueue,
      eventReporter,
    ),
    [addTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      deleteTeamsChatsQueue,
      eventReporter],
  );

  const addMultipleRemoteTeamsChats = React.useCallback(
    async (items: ITeamsChatsItem[]) => addMultipleRemoteTeamsChatsImpl(
      items,
      addTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      deleteTeamsChatsQueue,
      eventReporter,
    ),
    [addTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      deleteTeamsChatsQueue,
      eventReporter],
  );

  const deleteRemoteTeamsChats = React.useCallback(
    async (item: ITeamsChatsItem) => deleteRemoteTeamsChatsImpl(
      item,
      addTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      deleteTeamsChatsQueue,
      eventReporter,
    ),
    [addTeamsChatsQueue,
      getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      deleteTeamsChatsQueue,
      eventReporter],
  );

  const sendQueues = React.useCallback(
    async () => {
      const queues = await getTeamsChatsQueues?.();
      return sendTeamsChatsQueues(
        queues || null,
        postTeamsChatsApi,
        deleteTeamsChatsApi,
        deleteTeamsChatsQueue,
        eventReporter,
      );
    },
    [getTeamsChatsQueues,
      postTeamsChatsApi,
      deleteTeamsChatsApi,
      deleteTeamsChatsQueue,
      eventReporter],
  );

  const convertUserChatItems = React.useCallback(
    (items: IUserChatItem[]) => convertUserChatItemsToTeamsChatsItems(items),
    [],
  );

  return {
    addRemoteTeamsChats: addTeamsChatsQueue ? addRemoteTeamsChats : undefined,
    addMultipleRemoteTeamsChats: addTeamsChatsQueue ? addMultipleRemoteTeamsChats : undefined,
    deleteRemoteTeamsChats: addTeamsChatsQueue ? deleteRemoteTeamsChats : undefined,
    sendQueues: getTeamsChatsQueues ? sendQueues : undefined,
    convertUserChatItems,
  };
};

export default useRemoteTeamsChatsFeature;
